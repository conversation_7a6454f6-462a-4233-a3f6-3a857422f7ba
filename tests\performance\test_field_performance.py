#!/usr/bin/env python3
"""
Performance tests for field system
"""
import pytest
import time
from datetime import datetime, date

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.fields import Char, Integer, Float, <PERSON>ole<PERSON>, Date, Datetime, Selection


@pytest.mark.performance
@pytest.mark.slow
class TestFieldPerformance:
    """Performance tests for field validation"""
    
    def test_char_field_performance(self, performance_timer):
        """Test character field validation performance"""
        field = Char(size=100)
        test_values = ['test'] * 10000
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        # Should validate 10k values in reasonable time
        assert performance_timer.elapsed < 1.0  # Less than 1 second
        print(f"Char field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_integer_field_performance(self, performance_timer):
        """Test integer field validation performance"""
        field = Integer(min_value=0, max_value=1000000)
        test_values = list(range(10000))
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Integer field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_float_field_performance(self, performance_timer):
        """Test float field validation performance"""
        field = Float(digits=(10, 2))
        test_values = [i * 0.01 for i in range(10000)]
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Float field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_boolean_field_performance(self, performance_timer):
        """Test boolean field validation performance"""
        field = Boolean()
        test_values = [True, False, 'true', 'false', '1', '0'] * 1667  # ~10k values
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Boolean field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_date_field_performance(self, performance_timer):
        """Test date field validation performance"""
        field = Date()
        test_date = date(2023, 1, 15)
        test_values = [test_date] * 10000
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Date field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_datetime_field_performance(self, performance_timer):
        """Test datetime field validation performance"""
        field = Datetime()
        test_datetime = datetime(2023, 1, 15, 10, 30)
        test_values = [test_datetime] * 10000
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Datetime field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_selection_field_performance(self, performance_timer):
        """Test selection field validation performance"""
        selection = [('draft', 'Draft'), ('done', 'Done'), ('cancel', 'Cancelled')]
        field = Selection(selection)
        test_values = ['draft', 'done', 'cancel'] * 3334  # ~10k values
        
        performance_timer.start()
        for value in test_values:
            field.validate(value)
        performance_timer.stop()
        
        assert performance_timer.elapsed < 1.0
        print(f"Selection field validation: {performance_timer.elapsed:.4f}s for 10k values")
    
    def test_field_creation_performance(self, performance_timer):
        """Test field creation performance"""
        performance_timer.start()
        
        for i in range(1000):
            Char(string=f'Field {i}', size=100)
            Integer(string=f'Number {i}', min_value=0, max_value=1000)
            Boolean(string=f'Flag {i}', default=False)
            Date(string=f'Date {i}')
            Selection([('a', 'A'), ('b', 'B')], string=f'Choice {i}')
        
        performance_timer.stop()
        
        # Should create 5k fields in reasonable time
        assert performance_timer.elapsed < 2.0
        print(f"Field creation: {performance_timer.elapsed:.4f}s for 5k fields")
    
    def test_sql_type_generation_performance(self, performance_timer):
        """Test SQL type generation performance"""
        fields = [
            Char(size=100),
            Integer(),
            Float(digits=(10, 2)),
            Boolean(),
            Date(),
            Datetime(),
            Selection([('a', 'A'), ('b', 'B')])
        ] * 1000  # 7k fields
        
        performance_timer.start()
        for field in fields:
            field.get_sql_type()
        performance_timer.stop()
        
        assert performance_timer.elapsed < 0.5
        print(f"SQL type generation: {performance_timer.elapsed:.4f}s for 7k fields")
    
    @pytest.mark.slow
    def test_large_scale_validation(self, performance_timer):
        """Test large scale field validation"""
        # Create various field types
        fields = {
            'char': Char(size=255),
            'integer': Integer(min_value=0, max_value=1000000),
            'float': Float(digits=(10, 2)),
            'boolean': Boolean(),
            'date': Date(),
            'datetime': Datetime(),
            'selection': Selection([('a', 'A'), ('b', 'B'), ('c', 'C')])
        }
        
        # Test data
        test_data = {
            'char': ['test string'] * 10000,
            'integer': list(range(10000)),
            'float': [i * 0.01 for i in range(10000)],
            'boolean': [True, False] * 5000,
            'date': [date(2023, 1, 15)] * 10000,
            'datetime': [datetime(2023, 1, 15, 10, 30)] * 10000,
            'selection': ['a', 'b', 'c'] * 3334
        }
        
        performance_timer.start()
        
        for field_name, field in fields.items():
            for value in test_data[field_name]:
                field.validate(value)
        
        performance_timer.stop()
        
        # Should validate 70k values across all field types in reasonable time
        assert performance_timer.elapsed < 5.0
        print(f"Large scale validation: {performance_timer.elapsed:.4f}s for 70k validations")
        
        # Calculate operations per second
        total_operations = sum(len(values) for values in test_data.values())
        ops_per_second = total_operations / performance_timer.elapsed
        print(f"Validation rate: {ops_per_second:.0f} operations/second")
        
        # Should achieve reasonable throughput
        assert ops_per_second > 10000  # At least 10k ops/sec
