#!/usr/bin/env python3
"""
Unit tests for configuration system
"""
import pytest
import tempfile
import os

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.config import Config


@pytest.mark.unit
class TestConfig:
    """Test configuration system"""
    
    def test_config_creation(self):
        """Test config object creation"""
        config = Config()
        assert config is not None
        assert hasattr(config, 'config_file')
    
    def test_default_values(self):
        """Test default configuration values"""
        config = Config()
        
        # Test default database config
        db_config = config.db_config
        assert 'host' in db_config
        assert 'port' in db_config
        assert 'user' in db_config
        assert 'password' in db_config
        assert 'database' in db_config
        
        # Test default server config
        server_config = config.server_config
        assert 'host' in server_config
        assert 'port' in server_config
    
    def test_config_file_loading(self):
        """Test loading configuration from file"""
        # Create temporary config file
        config_content = """
[options]
db_host = test_host
db_port = 5433
db_user = test_user
db_password = test_pass
db_name = test_db
http_port = 8080
http_interface = 0.0.0.0
addons_path = test_addons
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write(config_content)
            config_file = f.name
        
        try:
            config = Config(config_file)
            
            # Test loaded values
            assert config.get('options', 'db_host') == 'test_host'
            assert config.get('options', 'db_port') == '5433'
            assert config.get('options', 'db_user') == 'test_user'
            assert config.get('options', 'db_password') == 'test_pass'
            assert config.get('options', 'db_name') == 'test_db'
            assert config.get('options', 'http_port') == '8080'
            assert config.get('options', 'http_interface') == '0.0.0.0'
            assert config.get('options', 'addons_path') == 'test_addons'
            
        finally:
            os.unlink(config_file)
    
    def test_db_config_property(self):
        """Test database configuration property"""
        config_content = """
[options]
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_db
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write(config_content)
            config_file = f.name
        
        try:
            config = Config(config_file)
            db_config = config.db_config
            
            assert db_config['host'] == 'localhost'
            assert db_config['port'] == 5432
            assert db_config['user'] == 'erp'
            assert db_config['password'] == 'erp'
            assert db_config['database'] == 'erp_db'
            
        finally:
            os.unlink(config_file)
    
    def test_server_config_property(self):
        """Test server configuration property"""
        config_content = """
[options]
http_port = 8069
http_interface = 127.0.0.1
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write(config_content)
            config_file = f.name
        
        try:
            config = Config(config_file)
            server_config = config.server_config
            
            assert server_config['host'] == '127.0.0.1'
            assert server_config['port'] == 8069
            
        finally:
            os.unlink(config_file)
    
    def test_addons_path_property(self):
        """Test addons path property"""
        config_content = """
[options]
addons_path = custom_addons
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write(config_content)
            config_file = f.name
        
        try:
            config = Config(config_file)
            assert config.addons_path == 'custom_addons'
            
        finally:
            os.unlink(config_file)
    
    def test_config_set_get(self):
        """Test setting and getting configuration values"""
        config = Config()
        
        # Test setting a value
        config.set('options', 'test_key', 'test_value')
        assert config.get('options', 'test_key') == 'test_value'
        
        # Test getting non-existent value with default
        assert config.get('options', 'non_existent', 'default') == 'default'
    
    def test_missing_config_file(self):
        """Test handling of missing configuration file"""
        config = Config('non_existent_file.conf')
        # Should not raise an exception, should use defaults
        assert config is not None
        
        # Should still have default values
        db_config = config.db_config
        assert 'host' in db_config
    
    def test_invalid_config_file(self):
        """Test handling of invalid configuration file"""
        # Create invalid config file
        invalid_content = "invalid config content [[[["
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.conf', delete=False) as f:
            f.write(invalid_content)
            config_file = f.name
        
        try:
            config = Config(config_file)
            # Should not raise an exception, should use defaults
            assert config is not None
            
        finally:
            os.unlink(config_file)
