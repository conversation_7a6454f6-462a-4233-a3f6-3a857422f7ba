#!/usr/bin/env python3
"""
Comprehensive tests for the enhanced field system
"""
import pytest
import uuid
from datetime import datetime, date
from decimal import Decimal

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from erp.fields import (
    Field, Char, Text, Integer, Float, Boolean, Date, Datetime,
    Selection, Binary, Json, Html, FieldValidationError
)


class TestBaseField:
    """Test base field functionality"""
    
    def test_field_creation(self):
        """Test basic field creation"""
        field = Field(string="Test Field", required=True, help="Test help")
        assert field.string == "Test Field"
        assert field.required is True
        assert field.help == "Test help"
        assert field.readonly is False
        assert field.index is False
    
    def test_default_value(self):
        """Test default value handling"""
        # Static default
        field = Field(default="test")
        assert field.get_default_value() == "test"
        
        # Callable default
        field = Field(default=lambda: "dynamic")
        assert field.get_default_value() == "dynamic"
    
    def test_validation_required(self):
        """Test required field validation"""
        field = Field(required=True)
        
        with pytest.raises(FieldValidationError):
            field.validate(None)
        
        assert field.validate("value") == "value"
    
    def test_validation_optional(self):
        """Test optional field validation"""
        field = Field(required=False)
        assert field.validate(None) is None
        assert field.validate("value") == "value"


class TestCharField:
    """Test character field"""
    
    def test_char_creation(self):
        """Test char field creation"""
        field = Char(size=50, string="Name")
        assert field.size == 50
        assert field.get_sql_type() == "VARCHAR(50)"
    
    def test_char_no_size(self):
        """Test char field without size"""
        field = Char()
        assert field.get_sql_type() == "TEXT"
    
    def test_char_validation(self):
        """Test char field validation"""
        field = Char(size=5)
        
        # Valid string
        assert field.validate("test") == "test"
        
        # String conversion
        assert field.validate(123) == "123"
        
        # Size validation
        with pytest.raises(FieldValidationError):
            field.validate("toolong")


class TestIntegerField:
    """Test integer field"""
    
    def test_integer_validation(self):
        """Test integer field validation"""
        field = Integer(min_value=0, max_value=100)
        
        # Valid integer
        assert field.validate(50) == 50
        
        # String conversion
        assert field.validate("25") == 25
        
        # Min value validation
        with pytest.raises(FieldValidationError):
            field.validate(-1)
        
        # Max value validation
        with pytest.raises(FieldValidationError):
            field.validate(101)
        
        # Invalid conversion
        with pytest.raises(FieldValidationError):
            field.validate("not_a_number")


class TestFloatField:
    """Test float field"""
    
    def test_float_validation(self):
        """Test float field validation"""
        field = Float(digits=(10, 2), min_value=0.0, max_value=999.99)
        
        # Valid float
        assert field.validate(123.45) == 123.45
        
        # Precision rounding
        assert field.validate(123.456) == 123.46
        
        # String conversion
        assert field.validate("123.45") == 123.45
        
        # Min value validation
        with pytest.raises(FieldValidationError):
            field.validate(-1.0)
        
        # Max value validation
        with pytest.raises(FieldValidationError):
            field.validate(1000.0)
    
    def test_float_sql_type(self):
        """Test float SQL type generation"""
        field = Float(digits=(10, 2))
        assert field.get_sql_type() == "NUMERIC(10,2)"
        
        field = Float()
        assert field.get_sql_type() == "REAL"


class TestBooleanField:
    """Test boolean field"""
    
    def test_boolean_validation(self):
        """Test boolean field validation"""
        field = Boolean()
        
        # Direct boolean
        assert field.validate(True) is True
        assert field.validate(False) is False
        
        # String conversion
        assert field.validate("true") is True
        assert field.validate("false") is False
        assert field.validate("1") is True
        assert field.validate("0") is False
        assert field.validate("") is False
        
        # Numeric conversion
        assert field.validate(1) is True
        assert field.validate(0) is False
        
        # Invalid conversion
        with pytest.raises(FieldValidationError):
            field.validate("invalid")


class TestDateField:
    """Test date field"""
    
    def test_date_validation(self):
        """Test date field validation"""
        field = Date()
        
        # Direct date
        test_date = date(2023, 1, 15)
        assert field.validate(test_date) == test_date
        
        # Datetime conversion
        test_datetime = datetime(2023, 1, 15, 10, 30)
        assert field.validate(test_datetime) == test_date
        
        # String conversion
        assert field.validate("2023-01-15") == test_date
        assert field.validate("15/01/2023") == test_date
        
        # Invalid string
        with pytest.raises(FieldValidationError):
            field.validate("invalid_date")


class TestDatetimeField:
    """Test datetime field"""
    
    def test_datetime_validation(self):
        """Test datetime field validation"""
        field = Datetime()
        
        # Direct datetime
        test_datetime = datetime(2023, 1, 15, 10, 30)
        assert field.validate(test_datetime) == test_datetime
        
        # Date conversion
        test_date = date(2023, 1, 15)
        expected = datetime.combine(test_date, datetime.min.time())
        assert field.validate(test_date) == expected
        
        # String conversion
        assert field.validate("2023-01-15 10:30:00") == test_datetime
        
        # Invalid string
        with pytest.raises(FieldValidationError):
            field.validate("invalid_datetime")


class TestSelectionField:
    """Test selection field"""
    
    def test_selection_validation(self):
        """Test selection field validation"""
        selection = [('draft', 'Draft'), ('done', 'Done')]
        field = Selection(selection)
        
        # Valid selection
        assert field.validate('draft') == 'draft'
        assert field.validate('done') == 'done'
        
        # Invalid selection
        with pytest.raises(FieldValidationError):
            field.validate('invalid')
    
    def test_selection_items(self):
        """Test selection items retrieval"""
        selection = [('draft', 'Draft'), ('done', 'Done')]
        field = Selection(selection)
        
        items = field.get_selection_items()
        assert items == [('draft', 'Draft'), ('done', 'Done')]
    
    def test_dynamic_selection(self):
        """Test dynamic selection"""
        def get_selection():
            return [('a', 'A'), ('b', 'B')]
        
        field = Selection(get_selection)
        assert field.validate('a') == 'a'
        
        items = field.get_selection_items()
        assert items == [('a', 'A'), ('b', 'B')]


# Related field tests removed - will be implemented later


class TestJsonField:
    """Test JSON field"""
    
    def test_json_validation(self):
        """Test JSON field validation"""
        field = Json()
        
        # Valid JSON data
        data = {'key': 'value', 'number': 123}
        assert field.validate(data) == data
        
        # List data
        list_data = [1, 2, 3]
        assert field.validate(list_data) == list_data
        
        # Invalid JSON data (non-serializable)
        with pytest.raises(FieldValidationError):
            field.validate(lambda x: x)


class TestHtmlField:
    """Test HTML field"""
    
    def test_html_validation(self):
        """Test HTML field validation"""
        field = Html(sanitize=True)
        
        # Safe HTML
        safe_html = "<p>Hello <strong>world</strong></p>"
        assert field.validate(safe_html) == safe_html
        
        # Sanitized HTML (script removal)
        unsafe_html = "<p>Hello</p><script>alert('xss')</script>"
        result = field.validate(unsafe_html)
        assert "<script>" not in result
        assert "<p>Hello</p>" in result


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
