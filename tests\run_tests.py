#!/usr/bin/env python3
"""
Test runner script for ERP system
"""
import sys
import os
import argparse
import subprocess
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_pytest(args):
    """Run pytest with given arguments"""
    cmd = ['python', '-m', 'pytest'] + args
    return subprocess.run(cmd, cwd=project_root)


def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='ERP System Test Runner')
    
    # Test selection options
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--performance', action='store_true', help='Run performance tests only')
    parser.add_argument('--all', action='store_true', help='Run all tests')
    
    # Test filtering options
    parser.add_argument('--module', '-m', help='Run tests for specific module')
    parser.add_argument('--pattern', '-k', help='Run tests matching pattern')
    parser.add_argument('--file', '-f', help='Run specific test file')
    
    # Output options
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--quiet', '-q', action='store_true', help='Quiet output')
    parser.add_argument('--coverage', action='store_true', help='Generate coverage report')
    parser.add_argument('--html-report', action='store_true', help='Generate HTML report')
    
    # Performance options
    parser.add_argument('--benchmark', action='store_true', help='Run benchmark tests')
    parser.add_argument('--profile', action='store_true', help='Profile test execution')
    
    # Parallel execution
    parser.add_argument('--parallel', '-n', type=int, help='Run tests in parallel')
    
    args = parser.parse_args()
    
    # Build pytest arguments
    pytest_args = []
    
    # Test selection
    if args.unit:
        pytest_args.extend(['-m', 'unit'])
    elif args.integration:
        pytest_args.extend(['-m', 'integration'])
    elif args.performance:
        pytest_args.extend(['-m', 'performance'])
    elif args.all:
        pass  # Run all tests
    else:
        # Default: run unit and integration tests, skip performance
        pytest_args.extend(['-m', 'not performance'])
    
    # Test filtering
    if args.module:
        pytest_args.extend(['-m', args.module])
    
    if args.pattern:
        pytest_args.extend(['-k', args.pattern])
    
    if args.file:
        pytest_args.append(args.file)
    
    # Output options
    if args.verbose:
        pytest_args.append('-v')
    elif args.quiet:
        pytest_args.append('-q')
    
    # Coverage
    if args.coverage:
        pytest_args.extend(['--cov=erp', '--cov-report=term-missing'])
        if args.html_report:
            pytest_args.append('--cov-report=html')
    
    # HTML report
    if args.html_report and not args.coverage:
        pytest_args.append('--html=tests/reports/report.html')
    
    # Parallel execution
    if args.parallel:
        pytest_args.extend(['-n', str(args.parallel)])
    
    # Benchmark
    if args.benchmark:
        pytest_args.append('--benchmark-only')
    
    # Profile
    if args.profile:
        pytest_args.append('--profile')
    
    # Add default test directory if no specific file
    if not args.file:
        pytest_args.append('tests/')
    
    print(f"Running tests with: pytest {' '.join(pytest_args)}")
    
    # Run the tests
    result = run_pytest(pytest_args)
    
    return result.returncode


if __name__ == '__main__':
    sys.exit(main())
