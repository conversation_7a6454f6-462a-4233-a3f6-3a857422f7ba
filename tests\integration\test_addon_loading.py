#!/usr/bin/env python3
"""
Integration tests for addon loading system
"""
import pytest
import os
import tempfile
import shutil

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.addons.loader import AddonLoader
from erp.addons.manifest import AddonManifest
from erp.models.base import ModelRegistry


@pytest.mark.integration
@pytest.mark.addon
class TestAddonLoading:
    """Test addon loading integration"""
    
    def test_addon_discovery(self, temp_addon_dir, create_test_addon):
        """Test addon discovery"""
        # Create test addons
        create_test_addon('test_addon1')
        create_test_addon('test_addon2')
        
        loader = AddonLoader(temp_addon_dir)
        addons = loader.discover_addons()
        
        assert 'test_addon1' in addons
        assert 'test_addon2' in addons
        assert len(addons) == 2
    
    def test_addon_manifest_loading(self, temp_addon_dir, create_test_addon):
        """Test addon manifest loading"""
        manifest_data = {
            'name': 'Custom Test Addon',
            'version': '2.0.0',
            'description': 'Custom test description',
            'author': 'Test Author',
            'depends': ['base'],
            'category': 'Test Category',
        }
        
        addon_path = create_test_addon('custom_addon', manifest_data)
        
        manifest = AddonManifest(addon_path)
        
        assert manifest.name == 'Custom Test Addon'
        assert manifest.version == '2.0.0'
        assert manifest.description == 'Custom test description'
        assert manifest.author == 'Test Author'
        assert manifest.depends == ['base']
        assert manifest.category == 'Test Category'
    
    def test_addon_dependency_resolution(self, temp_addon_dir, create_test_addon):
        """Test addon dependency resolution"""
        # Create base addon
        base_manifest = {
            'name': 'Base Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('base', base_manifest)
        
        # Create dependent addon
        dependent_manifest = {
            'name': 'Dependent Addon',
            'version': '1.0.0',
            'depends': ['base'],
        }
        create_test_addon('dependent', dependent_manifest)
        
        # Create addon with multiple dependencies
        multi_manifest = {
            'name': 'Multi Dependent Addon',
            'version': '1.0.0',
            'depends': ['base', 'dependent'],
        }
        create_test_addon('multi_dependent', multi_manifest)
        
        loader = AddonLoader(temp_addon_dir)
        load_order = loader.get_load_order()
        
        # Check that dependencies are loaded before dependents
        base_index = load_order.index('base')
        dependent_index = load_order.index('dependent')
        multi_index = load_order.index('multi_dependent')
        
        assert base_index < dependent_index
        assert dependent_index < multi_index
        assert base_index < multi_index
    
    def test_circular_dependency_detection(self, temp_addon_dir, create_test_addon):
        """Test circular dependency detection"""
        # Create addon A that depends on B
        addon_a_manifest = {
            'name': 'Addon A',
            'version': '1.0.0',
            'depends': ['addon_b'],
        }
        create_test_addon('addon_a', addon_a_manifest)
        
        # Create addon B that depends on A (circular)
        addon_b_manifest = {
            'name': 'Addon B',
            'version': '1.0.0',
            'depends': ['addon_a'],
        }
        create_test_addon('addon_b', addon_b_manifest)
        
        loader = AddonLoader(temp_addon_dir)
        
        # Should handle circular dependencies gracefully
        # (Implementation may vary - could raise exception or resolve partially)
        try:
            load_order = loader.get_load_order()
            # If no exception, check that both addons are included
            assert 'addon_a' in load_order
            assert 'addon_b' in load_order
        except Exception as e:
            # Circular dependency detection should provide meaningful error
            assert 'circular' in str(e).lower() or 'dependency' in str(e).lower()
    
    def test_missing_dependency_handling(self, temp_addon_dir, create_test_addon):
        """Test handling of missing dependencies"""
        # Create addon that depends on non-existent addon
        manifest = {
            'name': 'Addon with Missing Dep',
            'version': '1.0.0',
            'depends': ['non_existent_addon'],
        }
        create_test_addon('missing_dep_addon', manifest)
        
        loader = AddonLoader(temp_addon_dir)
        
        # Should handle missing dependencies gracefully
        try:
            load_order = loader.get_load_order()
            # May or may not include the addon with missing dependency
        except Exception as e:
            # Should provide meaningful error about missing dependency
            assert 'non_existent_addon' in str(e) or 'missing' in str(e).lower()
    
    def test_addon_loading_with_models(self, temp_addon_dir, create_test_addon, clean_registry):
        """Test addon loading with model definitions"""
        # Create addon with model
        addon_path = create_test_addon('model_addon')
        
        # Create a model file
        models_dir = os.path.join(addon_path, 'models')
        model_file = os.path.join(models_dir, 'test_model.py')
        
        model_content = '''
from erp.models.base import BaseModel
from erp.fields import Char, Integer

class TestAddonModel(BaseModel):
    _name = 'test.addon.model'
    _table = 'test_addon_model'
    
    description = Char(string='Description')
    value = Integer(string='Value')
'''
        
        with open(model_file, 'w') as f:
            f.write(model_content)
        
        # Update __init__.py to import the model
        init_file = os.path.join(addon_path, '__init__.py')
        with open(init_file, 'w') as f:
            f.write('from . import models\n')
        
        models_init = os.path.join(models_dir, '__init__.py')
        with open(models_init, 'w') as f:
            f.write('from . import test_model\n')
        
        # Load the addon
        loader = AddonLoader(temp_addon_dir)
        success = loader.load_addon('model_addon')
        
        assert success
        
        # Check that model is registered
        assert 'test.addon.model' in ModelRegistry._models
        
        # Test model functionality
        TestAddonModel = ModelRegistry.get('test.addon.model')
        record = TestAddonModel.create({
            'name': 'Test Record',
            'description': 'Test Description',
            'value': 42
        })
        
        assert record.name == 'Test Record'
        assert record.description == 'Test Description'
        assert record.value == 42
    
    def test_addon_uninstallable_flag(self, temp_addon_dir, create_test_addon):
        """Test addon installable flag"""
        # Create uninstallable addon
        manifest = {
            'name': 'Uninstallable Addon',
            'version': '1.0.0',
            'installable': False,
        }
        create_test_addon('uninstallable', manifest)
        
        # Create installable addon
        manifest2 = {
            'name': 'Installable Addon',
            'version': '1.0.0',
            'installable': True,
        }
        create_test_addon('installable', manifest2)
        
        loader = AddonLoader(temp_addon_dir)
        addons = loader.discover_addons()
        
        assert not addons['uninstallable'].installable
        assert addons['installable'].installable
    
    def test_addon_auto_install_flag(self, temp_addon_dir, create_test_addon):
        """Test addon auto_install flag"""
        manifest = {
            'name': 'Auto Install Addon',
            'version': '1.0.0',
            'auto_install': True,
        }
        create_test_addon('auto_install', manifest)
        
        loader = AddonLoader(temp_addon_dir)
        addons = loader.discover_addons()
        
        assert addons['auto_install'].auto_install
