"""
Tests for the new ERP testing framework
Verifies that the standardized import system and test infrastructure work correctly
"""
import sys
import os

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from erp.tests.common import TransactionCase, TestCase
from erp.tests.tags import tag, Tags
from erp.addons import get_addon_module, ensure_addon_import


@tag(Tags.UNIT, 'framework')
class TestStandardizedImports(TestCase):
    """Test the standardized addon import system"""
    
    def test_ensure_addon_import(self):
        """Test that ensure_addon_import works correctly"""
        # Test with base addon
        addons_path = 'addons'
        addon_path = os.path.join(addons_path, 'base')
        
        if os.path.exists(addon_path):
            result = ensure_addon_import('base', addon_path)
            self.assertTrue(result, "Base addon should be importable")
    
    def test_get_addon_module(self):
        """Test that get_addon_module works correctly"""
        # Ensure base addon is available
        addons_path = 'addons'
        addon_path = os.path.join(addons_path, 'base')
        
        if os.path.exists(addon_path):
            ensure_addon_import('base', addon_path)
            
            # Test getting the module
            base_module = get_addon_module('base')
            self.assertIsNotNone(base_module, "Base addon module should be available")
    
    def test_standardized_import_path(self):
        """Test that erp.addons.addon_name import path works"""
        # Ensure base addon is available
        addons_path = 'addons'
        addon_path = os.path.join(addons_path, 'base')
        
        if os.path.exists(addon_path):
            ensure_addon_import('base', addon_path)
            
            # Test importing via standardized path
            try:
                import erp.addons.base
                self.assertIsNotNone(erp.addons.base, "erp.addons.base should be importable")
            except ImportError:
                self.fail("erp.addons.base should be importable")


@tag(Tags.UNIT, 'framework')
class TestFrameworkClasses(TransactionCase):
    """Test the new testing framework base classes"""
    
    def test_transaction_case_setup(self):
        """Test that TransactionCase sets up correctly"""
        # Test that we have access to env
        self.assertIsNotNone(self.env, "TransactionCase should provide env")
        
        # Test that env behaves like expected
        self.assertIsInstance(self.env, object)
    
    def test_env_context_methods(self):
        """Test environment context methods"""
        # Test with_context
        new_env = self.env.with_context(test_key='test_value')
        self.assertIsNotNone(new_env, "with_context should return new environment")
        
        # Test with_user
        new_env = self.env.with_user(2)
        self.assertIsNotNone(new_env, "with_user should return new environment")
    
    def test_env_ref_method(self):
        """Test environment ref method"""
        # Test ref method (mock implementation)
        ref_result = self.env.ref('test.xml.id')
        self.assertIsNotNone(ref_result, "ref should return a result")


@tag(Tags.INTEGRATION, 'framework')
class TestFrameworkIntegration(TransactionCase):
    """Integration tests for the testing framework"""
    
    def test_addon_model_access(self):
        """Test accessing addon models through the framework"""
        # Skip if base addon is not available
        base_module = get_addon_module('base')
        if base_module is None:
            self.skipTest("Base addon not available")
        
        # Test accessing models through env
        try:
            # This would work if the models were properly registered
            # For now, just test that the framework doesn't crash
            model_name = 'ir.module.module'
            # model = self.env[model_name]  # Would work with full model registration
            self.assertTrue(True, "Framework should handle model access gracefully")
        except Exception as e:
            # Expected for now since models aren't fully registered in tests
            self.assertIsInstance(e, (KeyError, AttributeError), 
                                f"Expected KeyError or AttributeError, got {type(e)}")
    
    def test_test_isolation(self):
        """Test that test isolation works correctly"""
        # This test verifies that each test runs in isolation
        # by checking that changes don't persist between tests
        
        # Set a value that should not persist
        test_value = getattr(self, '_test_isolation_value', None)
        self.assertIsNone(test_value, "Test isolation should prevent value persistence")
        
        # Set a value for the next test (this should not persist)
        self._test_isolation_value = "test_value"
    
    def test_test_isolation_verification(self):
        """Verify that test isolation worked in previous test"""
        # This should not see the value set in the previous test
        test_value = getattr(self, '_test_isolation_value', None)
        self.assertIsNone(test_value, "Test isolation should prevent value persistence")


@tag(Tags.UNIT, 'tags')
class TestTagSystem(TestCase):
    """Test the tag system for test categorization"""
    
    def test_tag_decorator(self):
        """Test that tag decorator works"""
        # This test itself is tagged, so we can verify tags exist
        from erp.tests.tags import get_test_tags
        
        tags = get_test_tags(self.test_tag_decorator)
        self.assertIsInstance(tags, set, "get_test_tags should return a set")
        self.assertIn('tags', tags, "This test should have 'tags' tag")
    
    def test_tags_constants(self):
        """Test that Tags constants are available"""
        self.assertEqual(Tags.UNIT, 'unit')
        self.assertEqual(Tags.INTEGRATION, 'integration')
        self.assertEqual(Tags.PERFORMANCE, 'performance')
        self.assertEqual(Tags.BASE, 'base')
    
    def test_all_tags_method(self):
        """Test Tags.all_tags() method"""
        all_tags = Tags.all_tags()
        self.assertIsInstance(all_tags, set, "all_tags should return a set")
        self.assertIn('unit', all_tags, "all_tags should include 'unit'")
        self.assertIn('integration', all_tags, "all_tags should include 'integration'")


if __name__ == '__main__':
    import unittest
    unittest.main()
