#!/usr/bin/env python3
"""
Test script for ERP system
"""
import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from erp.config import config
from erp.addons.loader import AddonLoader
from erp.models.base import ModelRegistry


def test_config():
    """Test configuration system"""
    print("Testing configuration system...")
    print(f"Database config: {config.db_config}")
    print(f"Server config: {config.server_config}")
    print(f"Addons path: {config.addons_path}")
    print("✓ Configuration system working")


def test_addon_loading():
    """Test addon loading system"""
    print("\nTesting addon loading system...")
    
    loader = AddonLoader()
    addons = loader.discover_addons()
    print(f"Discovered addons: {list(addons.keys())}")
    
    # Test loading addons
    success = loader.load_addons()
    if success:
        loaded = loader.get_loaded_addons()
        print(f"Loaded addons: {list(loaded.keys())}")
        print("✓ Addon loading system working")
    else:
        print("✗ Addon loading failed")


def test_model_system():
    """Test model system"""
    print("\nTesting model system...")
    
    # Test model registry
    models = ModelRegistry.all()
    print(f"Registered models: {list(models.keys())}")
    
    # Test creating a module record
    if 'ir.module.module' in models:
        IrModuleModule = models['ir.module.module']
        try:
            module = IrModuleModule.create({
                'name': 'test_module',
                'display_name': 'Test Module',
                'description': 'A test module',
                'state': 'installed'
            })
            print(f"Created module: {module}")
            print(f"Module data: {module.read()}")
            print("✓ Model system working")
        except Exception as e:
            print(f"✗ Model creation failed: {e}")
    else:
        print("✗ ir.module.module not found in registry")

    # Test field definitions
    if 'ir.model.fields' in models:
        IrModelFields = models['ir.model.fields']
        try:
            field = IrModelFields.create({
                'name': 'test_field',
                'field_description': 'Test Field',
                'model': 'test.model',
                'ttype': 'char',
                'required': True
            })
            print(f"Created field: {field}")
            print(f"Field data: {field.read()}")
            print("✓ Field system working")
        except Exception as e:
            print(f"✗ Field creation failed: {e}")
    else:
        print("✗ ir.model.fields not found in registry")


def test_field_types():
    """Test field type system"""
    print("\nTesting field type system...")
    
    from erp.fields import Char, Integer, Boolean, Datetime
    
    # Test field creation
    char_field = Char(string='Test Char', required=True, size=50)
    int_field = Integer(string='Test Integer', default=0)
    bool_field = Boolean(string='Test Boolean', default=False)
    datetime_field = Datetime(string='Test Datetime')
    
    print(f"Char field: {char_field.string}, required: {char_field.required}")
    print(f"Integer field default: {int_field.get_default_value()}")
    print(f"Boolean field default: {bool_field.get_default_value()}")
    print(f"Datetime field default: {datetime_field.get_default_value()}")
    print("✓ Field type system working")


def main():
    """Run all tests"""
    print("ERP System Test Suite")
    print("=" * 50)
    
    try:
        test_config()
        test_addon_loading()
        test_model_system()
        test_field_types()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
