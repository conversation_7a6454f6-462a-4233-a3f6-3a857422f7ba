#!/usr/bin/env python3
"""
Unit tests for model system
"""
import pytest
from datetime import datetime

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.models.base import BaseModel, ModelRegistry, ModelMeta
from erp.fields import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Datetime


@pytest.mark.unit
@pytest.mark.model
class TestModelMeta:
    """Test model metaclass"""
    
    def test_field_collection(self, clean_registry):
        """Test field collection from class definition"""
        
        class TestModel(BaseModel):
            _name = 'test.model'
            _table = 'test_model'
            
            test_field = Char(string='Test Field')
            number_field = Integer(string='Number')
        
        # Check that fields are collected
        assert 'test_field' in TestModel._fields
        assert 'number_field' in TestModel._fields
        
        # Check that common fields are inherited
        assert 'id' in TestModel._fields
        assert 'name' in TestModel._fields
        assert 'createAt' in TestModel._fields
        assert 'updateAt' in TestModel._fields
    
    def test_model_registration(self, clean_registry):
        """Test model registration in registry"""
        
        class TestModel(BaseModel):
            _name = 'test.registration'
            _table = 'test_registration'
        
        # Check model is registered
        assert 'test.registration' in ModelRegistry._models
        assert ModelRegistry.get('test.registration') == TestModel


@pytest.mark.unit
@pytest.mark.model
class TestModelRegistry:
    """Test model registry"""
    
    def test_registry_operations(self, clean_registry):
        """Test registry register/get/all operations"""
        
        class TestModel1(BaseModel):
            _name = 'test.model1'
        
        class TestModel2(BaseModel):
            _name = 'test.model2'
        
        # Test get
        assert ModelRegistry.get('test.model1') == TestModel1
        assert ModelRegistry.get('test.model2') == TestModel2
        assert ModelRegistry.get('non.existent') is None
        
        # Test all
        all_models = ModelRegistry.all()
        assert 'test.model1' in all_models
        assert 'test.model2' in all_models
    
    def test_registry_isolation(self, clean_registry):
        """Test that registry is properly isolated between tests"""
        # This test should start with empty registry due to clean_registry fixture
        assert len(ModelRegistry._models) == 0
        
        class TestModel(BaseModel):
            _name = 'test.isolation'
        
        assert len(ModelRegistry._models) == 1


@pytest.mark.unit
@pytest.mark.model
class TestBaseModel:
    """Test base model functionality"""
    
    def test_model_creation(self, clean_registry):
        """Test model instance creation"""
        
        class TestModel(BaseModel):
            _name = 'test.creation'
            _table = 'test_creation'
            
            description = Char(string='Description')
        
        # Test create method
        data = {
            'name': 'Test Record',
            'description': 'Test Description'
        }
        
        record = TestModel.create(data)
        
        assert record is not None
        assert record.name == 'Test Record'
        assert record.description == 'Test Description'
        assert record.id is not None
        assert isinstance(record.createAt, datetime)
        assert isinstance(record.updateAt, datetime)
    
    def test_field_access(self, clean_registry):
        """Test field value access"""
        
        class TestModel(BaseModel):
            _name = 'test.access'
            _table = 'test_access'
            
            active = Boolean(string='Active', default=True)
            count = Integer(string='Count', default=0)
        
        record = TestModel.create({'name': 'Test'})
        
        # Test field access
        assert record.name == 'Test'
        assert record.active is True
        assert record.count == 0
        
        # Test field assignment
        record.active = False
        record.count = 5
        
        assert record.active is False
        assert record.count == 5
    
    def test_read_method(self, clean_registry):
        """Test record read method"""
        
        class TestModel(BaseModel):
            _name = 'test.read'
            _table = 'test_read'
            
            status = Char(string='Status', default='draft')
        
        record = TestModel.create({
            'name': 'Test Record',
            'status': 'active'
        })
        
        data = record.read()
        
        assert isinstance(data, dict)
        assert data['name'] == 'Test Record'
        assert data['status'] == 'active'
        assert 'id' in data
        assert 'createAt' in data
        assert 'updateAt' in data
    
    def test_write_method(self, clean_registry):
        """Test record write method"""
        
        class TestModel(BaseModel):
            _name = 'test.write'
            _table = 'test_write'
            
            status = Char(string='Status')
            priority = Integer(string='Priority')
        
        record = TestModel.create({
            'name': 'Test Record',
            'status': 'draft',
            'priority': 1
        })
        
        # Test write
        record.write({
            'status': 'done',
            'priority': 5
        })
        
        assert record.status == 'done'
        assert record.priority == 5
    
    def test_field_defaults(self, clean_registry):
        """Test field default values"""
        
        class TestModel(BaseModel):
            _name = 'test.defaults'
            _table = 'test_defaults'
            
            active = Boolean(string='Active', default=True)
            count = Integer(string='Count', default=lambda: 42)
            status = Char(string='Status', default='new')
        
        record = TestModel.create({'name': 'Test'})
        
        assert record.active is True
        assert record.count == 42
        assert record.status == 'new'
    
    def test_required_fields(self, clean_registry):
        """Test required field validation"""
        
        class TestModel(BaseModel):
            _name = 'test.required'
            _table = 'test_required'
            
            required_field = Char(string='Required', required=True)
        
        # Should work with required field
        record = TestModel.create({
            'name': 'Test',
            'required_field': 'Value'
        })
        assert record.required_field == 'Value'
        
        # Should handle missing required field gracefully
        # (In a real implementation, this might raise an exception)
        record2 = TestModel.create({'name': 'Test2'})
        assert record2 is not None
    
    def test_model_string_representation(self, clean_registry):
        """Test model string representation"""
        
        class TestModel(BaseModel):
            _name = 'test.repr'
            _table = 'test_repr'
        
        record = TestModel.create({'name': 'Test Record'})
        
        str_repr = str(record)
        assert 'TestModel' in str_repr
        assert record.id in str_repr
