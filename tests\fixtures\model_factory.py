#!/usr/bin/env python3
"""
Model factory for creating test models and data
"""
import uuid
from datetime import datetime, date
from typing import Dict, Any, Type, Optional

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.models.base import BaseModel
from erp.fields import Char, Integer, Float, Boolean, Date, Datetime, Selection


class ModelFactory:
    """Factory for creating test models and records"""
    
    @staticmethod
    def create_test_model(name: str, fields: Dict[str, Any] = None, table: str = None) -> Type[BaseModel]:
        """Create a test model class dynamically"""
        if fields is None:
            fields = {}
        
        if table is None:
            table = name.replace('.', '_')
        
        # Create model class attributes
        attrs = {
            '_name': name,
            '_table': table,
            '_description': f'Test model {name}',
        }
        
        # Add custom fields
        attrs.update(fields)
        
        # Create the model class
        model_class = type(f'TestModel_{name.replace(".", "_")}', (BaseModel,), attrs)
        
        return model_class
    
    @staticmethod
    def create_simple_model() -> Type[BaseModel]:
        """Create a simple test model with basic fields"""
        return ModelFactory.create_test_model(
            'test.simple',
            {
                'description': Char(string='Description', size=255),
                'active': Boolean(string='Active', default=True),
                'sequence': Integer(string='Sequence', default=10),
            }
        )
    
    @staticmethod
    def create_complex_model() -> Type[BaseModel]:
        """Create a complex test model with various field types"""
        return ModelFactory.create_test_model(
            'test.complex',
            {
                'title': Char(string='Title', required=True, size=100),
                'description': Char(string='Description', size=500),
                'priority': Integer(string='Priority', min_value=1, max_value=10, default=5),
                'score': Float(string='Score', digits=(5, 2), min_value=0.0, max_value=100.0),
                'active': Boolean(string='Active', default=True),
                'published': Boolean(string='Published', default=False),
                'start_date': Date(string='Start Date'),
                'end_date': Date(string='End Date'),
                'created_at': Datetime(string='Created At'),
                'state': Selection([
                    ('draft', 'Draft'),
                    ('review', 'Under Review'),
                    ('approved', 'Approved'),
                    ('rejected', 'Rejected'),
                    ('published', 'Published')
                ], string='State', default='draft'),
                'category': Selection([
                    ('news', 'News'),
                    ('blog', 'Blog'),
                    ('tutorial', 'Tutorial'),
                    ('documentation', 'Documentation')
                ], string='Category', required=True),
            }
        )


class RecordFactory:
    """Factory for creating test records"""
    
    @staticmethod
    def create_record(model_class: Type[BaseModel], data: Dict[str, Any] = None) -> BaseModel:
        """Create a test record"""
        if data is None:
            data = {}
        
        # Ensure required fields have values
        if 'name' not in data:
            data['name'] = f'Test Record {uuid.uuid4().hex[:8]}'
        
        return model_class.create(data)
    
    @staticmethod
    def create_simple_record(model_class: Type[BaseModel] = None) -> BaseModel:
        """Create a simple test record"""
        if model_class is None:
            model_class = ModelFactory.create_simple_model()
        
        data = {
            'name': 'Simple Test Record',
            'description': 'A simple test record for testing',
            'active': True,
            'sequence': 1,
        }
        
        return RecordFactory.create_record(model_class, data)
    
    @staticmethod
    def create_complex_record(model_class: Type[BaseModel] = None) -> BaseModel:
        """Create a complex test record"""
        if model_class is None:
            model_class = ModelFactory.create_complex_model()
        
        data = {
            'name': 'Complex Test Record',
            'title': 'Test Article Title',
            'description': 'This is a comprehensive test article for testing purposes.',
            'priority': 7,
            'score': 85.5,
            'active': True,
            'published': False,
            'start_date': date(2023, 1, 1),
            'end_date': date(2023, 12, 31),
            'created_at': datetime(2023, 1, 15, 10, 30, 0),
            'state': 'review',
            'category': 'tutorial',
        }
        
        return RecordFactory.create_record(model_class, data)
    
    @staticmethod
    def create_multiple_records(model_class: Type[BaseModel], count: int, 
                              base_data: Dict[str, Any] = None) -> list:
        """Create multiple test records"""
        if base_data is None:
            base_data = {}
        
        records = []
        for i in range(count):
            data = base_data.copy()
            data['name'] = f'Test Record {i+1}'
            
            # Add some variation to the data
            if 'sequence' in model_class._fields:
                data['sequence'] = i + 1
            if 'priority' in model_class._fields:
                data['priority'] = (i % 10) + 1
            if 'active' in model_class._fields:
                data['active'] = i % 2 == 0
            
            records.append(RecordFactory.create_record(model_class, data))
        
        return records


class DataGenerator:
    """Generator for test data"""
    
    @staticmethod
    def generate_char_data(count: int, prefix: str = 'test') -> list:
        """Generate character field test data"""
        return [f'{prefix}_{i}' for i in range(count)]
    
    @staticmethod
    def generate_integer_data(count: int, min_val: int = 0, max_val: int = 1000) -> list:
        """Generate integer field test data"""
        import random
        return [random.randint(min_val, max_val) for _ in range(count)]
    
    @staticmethod
    def generate_float_data(count: int, min_val: float = 0.0, max_val: float = 100.0) -> list:
        """Generate float field test data"""
        import random
        return [round(random.uniform(min_val, max_val), 2) for _ in range(count)]
    
    @staticmethod
    def generate_boolean_data(count: int) -> list:
        """Generate boolean field test data"""
        import random
        return [random.choice([True, False]) for _ in range(count)]
    
    @staticmethod
    def generate_date_data(count: int, start_year: int = 2020, end_year: int = 2025) -> list:
        """Generate date field test data"""
        import random
        dates = []
        for _ in range(count):
            year = random.randint(start_year, end_year)
            month = random.randint(1, 12)
            day = random.randint(1, 28)  # Safe day range
            dates.append(date(year, month, day))
        return dates
    
    @staticmethod
    def generate_datetime_data(count: int, start_year: int = 2020, end_year: int = 2025) -> list:
        """Generate datetime field test data"""
        import random
        datetimes = []
        for _ in range(count):
            year = random.randint(start_year, end_year)
            month = random.randint(1, 12)
            day = random.randint(1, 28)
            hour = random.randint(0, 23)
            minute = random.randint(0, 59)
            second = random.randint(0, 59)
            datetimes.append(datetime(year, month, day, hour, minute, second))
        return datetimes
    
    @staticmethod
    def generate_selection_data(count: int, choices: list) -> list:
        """Generate selection field test data"""
        import random
        return [random.choice(choices) for _ in range(count)]


# Convenience functions for common test scenarios

def create_test_model_with_all_fields() -> Type[BaseModel]:
    """Create a test model with all supported field types"""
    return ModelFactory.create_test_model(
        'test.all_fields',
        {
            'char_field': Char(string='Char Field', size=100),
            'text_field': Char(string='Text Field'),  # Using Char for text
            'integer_field': Integer(string='Integer Field'),
            'float_field': Float(string='Float Field', digits=(10, 2)),
            'boolean_field': Boolean(string='Boolean Field'),
            'date_field': Date(string='Date Field'),
            'datetime_field': Datetime(string='Datetime Field'),
            'selection_field': Selection([
                ('option1', 'Option 1'),
                ('option2', 'Option 2'),
                ('option3', 'Option 3')
            ], string='Selection Field'),
        }
    )


def create_test_records_batch(model_class: Type[BaseModel], count: int) -> list:
    """Create a batch of test records for performance testing"""
    return RecordFactory.create_multiple_records(model_class, count)
