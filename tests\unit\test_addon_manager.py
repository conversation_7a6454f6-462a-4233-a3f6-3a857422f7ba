#!/usr/bin/env python3
"""
Unit tests for enhanced addon manager
"""
import pytest
import os
import tempfile
import shutil
import json
from datetime import datetime

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from erp.addons.manager import AddonManager, AddonState, AddonInfo, DependencyError, CircularDependencyError, MissingDependencyError
from erp.addons.manifest import AddonManifest


@pytest.mark.unit
@pytest.mark.addon
class TestAddonManager:
    """Test enhanced addon manager"""
    
    def test_addon_manager_creation(self, temp_addon_dir):
        """Test addon manager creation"""
        manager = AddonManager(temp_addon_dir)
        assert manager.addons_path == temp_addon_dir
        assert isinstance(manager._addon_states, dict)
    
    def test_discover_addons(self, temp_addon_dir, create_test_addon):
        """Test addon discovery"""
        # Create test addons
        create_test_addon('addon1')
        create_test_addon('addon2')
        
        manager = AddonManager(temp_addon_dir)
        addons = manager.discover_addons()
        
        assert 'addon1' in addons
        assert 'addon2' in addons
        assert len(addons) == 2
        
        # Check addon info structure
        addon_info = addons['addon1']
        assert isinstance(addon_info, AddonInfo)
        assert addon_info.name == 'addon1'
        assert addon_info.state == AddonState.UNINSTALLED
        assert addon_info.available_version is not None
    
    def test_dependency_checking(self, temp_addon_dir, create_test_addon):
        """Test dependency checking"""
        # Create base addon
        base_manifest = {
            'name': 'Base Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('base', base_manifest)
        
        # Create dependent addon
        dependent_manifest = {
            'name': 'Dependent Addon',
            'version': '1.0.0',
            'depends': ['base'],
        }
        create_test_addon('dependent', dependent_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Check dependencies for dependent addon
        deps_ok, errors = manager.check_dependencies('dependent')
        assert deps_ok
        assert len(errors) == 0
        
        # Check dependencies for non-existent addon
        deps_ok, errors = manager.check_dependencies('non_existent')
        assert not deps_ok
        assert len(errors) > 0
    
    def test_circular_dependency_detection(self, temp_addon_dir, create_test_addon):
        """Test circular dependency detection"""
        # Create addon A that depends on B
        addon_a_manifest = {
            'name': 'Addon A',
            'version': '1.0.0',
            'depends': ['addon_b'],
        }
        create_test_addon('addon_a', addon_a_manifest)
        
        # Create addon B that depends on A (circular)
        addon_b_manifest = {
            'name': 'Addon B',
            'version': '1.0.0',
            'depends': ['addon_a'],
        }
        create_test_addon('addon_b', addon_b_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Should detect circular dependency
        cycles = manager.detect_circular_dependencies(['addon_a', 'addon_b'])
        assert len(cycles) > 0
        
        # Should raise error when getting install order
        with pytest.raises(CircularDependencyError):
            manager.get_install_order(['addon_a', 'addon_b'])
    
    def test_install_order_calculation(self, temp_addon_dir, create_test_addon):
        """Test installation order calculation"""
        # Create addon hierarchy: base -> middle -> top
        base_manifest = {
            'name': 'Base Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('base', base_manifest)
        
        middle_manifest = {
            'name': 'Middle Addon',
            'version': '1.0.0',
            'depends': ['base'],
        }
        create_test_addon('middle', middle_manifest)
        
        top_manifest = {
            'name': 'Top Addon',
            'version': '1.0.0',
            'depends': ['middle'],
        }
        create_test_addon('top', top_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Get install order
        install_order = manager.get_install_order(['top'])
        
        # Should install in correct order
        base_index = install_order.index('base')
        middle_index = install_order.index('middle')
        top_index = install_order.index('top')
        
        assert base_index < middle_index < top_index
    
    def test_addon_installation(self, temp_addon_dir, create_test_addon):
        """Test addon installation"""
        # Create simple addon
        create_test_addon('simple_addon')
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Install addon
        success = manager.install_addon('simple_addon')
        assert success
        
        # Check state
        addon_info = manager.get_addon_info('simple_addon')
        assert addon_info.state == AddonState.INSTALLED
        assert addon_info.installed_version is not None
        assert addon_info.install_date is not None
    
    def test_addon_installation_with_dependencies(self, temp_addon_dir, create_test_addon):
        """Test addon installation with dependencies"""
        # Create base addon
        base_manifest = {
            'name': 'Base Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('base', base_manifest)
        
        # Create dependent addon
        dependent_manifest = {
            'name': 'Dependent Addon',
            'version': '1.0.0',
            'depends': ['base'],
        }
        create_test_addon('dependent', dependent_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Install dependent addon (should install base first)
        success = manager.install_addon('dependent')
        assert success
        
        # Check both addons are installed
        base_info = manager.get_addon_info('base')
        dependent_info = manager.get_addon_info('dependent')
        
        assert base_info.state == AddonState.INSTALLED
        assert dependent_info.state == AddonState.INSTALLED
    
    def test_addon_uninstallation(self, temp_addon_dir, create_test_addon):
        """Test addon uninstallation"""
        # Create and install addon
        create_test_addon('test_addon')
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        manager.install_addon('test_addon')
        
        # Uninstall addon
        success = manager.uninstall_addon('test_addon')
        assert success
        
        # Check state
        addon_info = manager.get_addon_info('test_addon')
        assert addon_info.state == AddonState.UNINSTALLED
        assert addon_info.installed_version is None
    
    def test_addon_uninstallation_with_dependents(self, temp_addon_dir, create_test_addon):
        """Test addon uninstallation with dependents"""
        # Create base and dependent addons
        base_manifest = {
            'name': 'Base Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('base', base_manifest)
        
        dependent_manifest = {
            'name': 'Dependent Addon',
            'version': '1.0.0',
            'depends': ['base'],
        }
        create_test_addon('dependent', dependent_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        manager.install_addon('dependent')  # This installs both
        
        # Try to uninstall base (should fail due to dependent)
        success = manager.uninstall_addon('base')
        assert not success
        
        # Force uninstall should work
        success = manager.uninstall_addon('base', force=True)
        assert success
        
        # Both should be uninstalled
        base_info = manager.get_addon_info('base')
        dependent_info = manager.get_addon_info('dependent')
        
        assert base_info.state == AddonState.UNINSTALLED
        assert dependent_info.state == AddonState.UNINSTALLED
    
    def test_addon_upgrade(self, temp_addon_dir, create_test_addon):
        """Test addon upgrade"""
        # Create and install addon
        manifest = {
            'name': 'Test Addon',
            'version': '1.0.0',
            'depends': [],
        }
        create_test_addon('test_addon', manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        manager.install_addon('test_addon')
        
        # Update manifest to new version
        new_manifest = manifest.copy()
        new_manifest['version'] = '2.0.0'
        
        addon_path = os.path.join(temp_addon_dir, 'test_addon')
        manifest_file = os.path.join(addon_path, '__manifest__.py')
        with open(manifest_file, 'w') as f:
            f.write(repr(new_manifest))
        
        # Rediscover addons
        manager.discover_addons()
        
        # Check that upgrade is needed
        addon_info = manager.get_addon_info('test_addon')
        assert addon_info.state == AddonState.TO_UPGRADE
        
        # Upgrade addon
        success = manager.upgrade_addon('test_addon')
        assert success
        
        # Check state
        addon_info = manager.get_addon_info('test_addon')
        assert addon_info.state == AddonState.INSTALLED
        assert addon_info.installed_version == '2.0.0'
    
    def test_addon_state_persistence(self, temp_addon_dir, create_test_addon):
        """Test addon state persistence"""
        # Create and install addon
        create_test_addon('persistent_addon')
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        manager.install_addon('persistent_addon')
        
        # Create new manager instance
        manager2 = AddonManager(temp_addon_dir)
        manager2.discover_addons()
        
        # Check that state is preserved
        addon_info = manager2.get_addon_info('persistent_addon')
        assert addon_info.state == AddonState.INSTALLED
    
    def test_dependency_tree(self, temp_addon_dir, create_test_addon):
        """Test dependency tree generation"""
        # Create addon hierarchy
        base_manifest = {'name': 'Base', 'version': '1.0.0', 'depends': []}
        middle_manifest = {'name': 'Middle', 'version': '1.0.0', 'depends': ['base']}
        top_manifest = {'name': 'Top', 'version': '1.0.0', 'depends': ['middle']}
        
        create_test_addon('base', base_manifest)
        create_test_addon('middle', middle_manifest)
        create_test_addon('top', top_manifest)
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Get dependency tree
        tree = manager.get_dependency_tree('top')
        
        assert tree['name'] == 'top'
        assert len(tree['dependencies']) == 1
        assert tree['dependencies'][0]['name'] == 'middle'
        assert len(tree['dependencies'][0]['dependencies']) == 1
        assert tree['dependencies'][0]['dependencies'][0]['name'] == 'base'
    
    def test_addon_integrity_validation(self, temp_addon_dir, create_test_addon):
        """Test addon integrity validation"""
        # Create valid addon
        create_test_addon('valid_addon')
        
        manager = AddonManager(temp_addon_dir)
        manager.discover_addons()
        
        # Validate valid addon
        is_valid, errors = manager.validate_addon_integrity('valid_addon')
        assert is_valid
        assert len(errors) == 0
        
        # Remove __init__.py to make it invalid
        addon_path = os.path.join(temp_addon_dir, 'valid_addon')
        init_file = os.path.join(addon_path, '__init__.py')
        os.remove(init_file)
        
        # Validate invalid addon
        is_valid, errors = manager.validate_addon_integrity('valid_addon')
        assert not is_valid
        assert len(errors) > 0
