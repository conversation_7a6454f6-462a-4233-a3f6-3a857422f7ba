[tool:pytest]
# Pytest configuration file

# Test discovery
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --strict-config
    --disable-warnings
    --color=yes
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests
    integration: Integration tests
    async_test: Asynchronous tests
    performance: Performance tests
    slow: Slow running tests
    database: Tests requiring database
    addon: Addon-specific tests
    field: Field system tests
    model: Model system tests
    server: Server tests
    api: API tests

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage options (when pytest-cov is installed)
# addopts = --cov=erp --cov-report=html --cov-report=term-missing

# Asyncio configuration
asyncio_mode = auto

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Filter warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:asyncio
